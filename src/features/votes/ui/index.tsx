"use client"

import React from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/ui/select"

type ConnectedVotesFilterProps = {
  value?: string
  onValueChange?: (value: string) => void
  className?: string
}

export function ConnectedVotesFilter({ value, onValueChange, className }: ConnectedVotesFilterProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder="Biriktirilgan ovozlar" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="none">Barchasi</SelectItem>
          <SelectItem value="connected">Biriktirilgan ovozlar</SelectItem>
          <SelectItem value="not_connected">Biriktirilmagan ovozlar</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}
