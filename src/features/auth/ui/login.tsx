"use client"

import { useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/shared/ui/card"
import { Input } from "@/shared/ui/input"
import { Label } from "@/shared/ui/label"
import { Alert, AlertDescription } from "@/shared/ui/alert"
import {login} from "@/entity/users/api/login";

export function Login() {
    const searchParams = useSearchParams()
    const [error, setError] = useState<string | null>(null)
    const [loading, setLoading] = useState(false)
    const justRegistered = searchParams.get("registered") === "true"

    async function handleSubmit(formData: FormData) {
        setLoading(true)
        setError(null)

        try {
            const result = await login(formData)

            if (result.success) {
                window.location.href = "/dashboard"
            } else {
                setError(result.error || "Invalid email or password")
            }
        } catch (err) {
            console.log(err)
            setError("An unexpected error occurred. Please try again.")
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="flex min-h-[80vh] items-center justify-center px-4">
            <Card className="w-full max-w-md">
                <CardHeader>
                    <CardTitle className="text-2xl">Kirish</CardTitle>
                    <CardDescription>Hisobingizga kirish uchun elektron pochta va parolingizni kiriting</CardDescription>
                </CardHeader>
                <CardContent>
                    {justRegistered && (
                        <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
                            <AlertDescription>Hisob yaratildi! Iltimos, tizimga kiring.</AlertDescription>
                        </Alert>
                    )}

                    {error && (
                        <Alert variant="destructive" className="mb-4">
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    <form action={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="email">Email</Label>
                            <Input
                                id="email"
                                name="email"
                                type="email"
                                placeholder="<EMAIL>"
                                required
                                autoComplete="email"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="password">Parol</Label>
                            <Input id="password" name="password" type="password" required autoComplete="current-password" />
                        </div>
                        <Button type="submit" className="w-full" disabled={loading}>
                            {loading ? "Kirish bajarilyapti..." : "Kirish"}
                        </Button>
                    </form>
                </CardContent>
                {/*<CardFooter className="flex justify-center">*/}
                {/*    <p className="text-sm text-muted-foreground">*/}
                {/*        {"Hisobingiz yo‘qmi?"}{" "}*/}
                {/*        <Link href="/register" className="text-primary hover:underline">*/}
                {/*            Hisob yaratish*/}
                {/*        </Link>*/}
                {/*    </p>*/}
                {/*</CardFooter>*/}
            </Card>
        </div>
    )
}

