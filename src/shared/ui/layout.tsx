import {ReactNode} from "react";
import {Sidebar, <PERSON>bar<PERSON>ontent, SidebarProvider, SidebarTrigger} from "@/shared/ui/sidebar";
import {ThemeToggle} from "@/shared/ui/theme-toggle";
import {Logout} from "@/features/auth/ui/logout";

type LayoutProps = {
  children: ReactNode
  content?: ReactNode
}

export function Layout({children, content}: LayoutProps) {
  return (
    <SidebarProvider>
      <Sidebar>
        <SidebarContent>
          {content}
        </SidebarContent>
      </Sidebar>
      <main className='w-full'>
        <SidebarTrigger className='fixed'/>
        <div className="flex gap-1 justify-end mb-2">
          <ThemeToggle/>
          <Logout/>
        </div>
        <div className='container mx-auto py-4 px-4'>
          {children}
        </div>
      </main>
    </SidebarProvider>
  )
}