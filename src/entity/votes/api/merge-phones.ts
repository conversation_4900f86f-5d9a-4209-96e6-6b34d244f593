'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";
import { MergePhonesRequest } from "../model/biriktirish-schema";

/**
 * Объединяет найденные голоса с выбранным человеком
 */
export async function mergePhones(request: MergePhonesRequest) {
    try {
        await dbConnect();

        const { votes, ownerId, updatedBy } = request;
        const voteIds = votes.map(v => v.voteId);

        // Проверяем, что все голоса существуют и не имеют привязки к другому владельцу или номера телефона
        const existingVotes = await VotesSchema.find({
            _id: { $in: voteIds },
            $or: [
                { ownerId: { $exists: false } },
                { ownerId: null }
            ],
            $and: [
                {
                    $or: [
                        { phoneNumber: { $exists: false } },
                        { phoneNumber: null },
                        { phoneNumber: '' }
                    ]
                }
            ]
        });

        if (existingVotes.length !== voteIds.length) {
            return {
                success: false,
                error: 'Ba\'zi ovozlar topilmadi, allaqachon boshqa egaga biriktirilgan yoki telefon raqami mavjud'
            };
        }

        // Обновляем каждый голос индивидуально с соответствующим номером телефона
        let mergedCount = 0;

        for (const vote of votes) {
            const updateData: any = {
                ownerId,
                phoneNumber: vote.phoneNumber
            };

            if (updatedBy) {
                updateData.updatedBy = updatedBy;
            }

            const updateResult = await VotesSchema.updateOne(
                { _id: vote.voteId },
                { $set: updateData }
            );

            if (updateResult.modifiedCount > 0) {
                mergedCount++;
            }
        }

        return {
            success: true,
            data: {
                mergedCount,
                ownerId
            }
        };
    } catch (error) {
        console.error('Error merging phones:', error);
        return {
            success: false,
            error: (error as Error).message || 'Failed to merge phones'
        };
    }
}
