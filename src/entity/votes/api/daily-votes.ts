'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";

export type DailyVoteData = {
    date: string;
    count: number;
    votes: {
        _id: string;
        originalPhoneNumber: string;
        voteDate: string;
        phoneNumber?: string;
        ownerId?: string;
        owner?: { _id: string; name: string };
        updatedBy?: string;
        createdAt: string;
        updatedAt: string;
    }[];
};

export type DailyVotesResult = {
    dailyData: DailyVoteData[];
    totalVotes: number;
    totalDays: number;
};

/**
 * Получает голоса, сгруппированные по дням
 */
// Функция для извлечения даты из строки
function extractDateFromString(dateStr: string): string {
    if (!dateStr || dateStr.trim() === '') {
        return 'Sana ko\'rsatilmagan';
    }

    // Попробуем найти дату в формате YYYY-MM-DD
    const standardDateMatch = dateStr.match(/(\d{4}-\d{2}-\d{2})/);
    if (standardDateMatch) {
        return standardDateMatch[1];
    }

    // Попробуем найти дату в формате DD-MM-YYYY или DD/MM/YYYY
    const reverseDateMatch = dateStr.match(/(\d{2})[\/\-](\d{2})[\/\-](\d{4})/);
    if (reverseDateMatch) {
        return `${reverseDateMatch[3]}-${reverseDateMatch[2]}-${reverseDateMatch[1]}`;
    }

    // Если ничего не найдено, возвращаем исходную строку
    return dateStr;
}

export async function getDailyVotes(): Promise<DailyVotesResult> {
    await dbConnect();

    try {
        // Получаем все голоса с информацией о владельцах
        const votes = await VotesSchema.aggregate([
            {
                $lookup: {
                    from: 'villagepeople',
                    localField: 'ownerId',
                    foreignField: '_id',
                    as: 'ownerInfo'
                }
            }
        ]);

        // Группируем голоса по дням в JavaScript
        const groupedByDate: { [key: string]: any[] } = {};

        votes.forEach((vote) => {
            const dateKey = extractDateFromString(vote.voteDate);

            if (!groupedByDate[dateKey]) {
                groupedByDate[dateKey] = [];
            }

            groupedByDate[dateKey].push({
                _id: vote._id.toString(),
                originalPhoneNumber: vote.originalPhoneNumber,
                voteDate: vote.voteDate || '',
                phoneNumber: vote.phoneNumber || undefined,
                ownerId: vote.ownerId ? vote.ownerId.toString() : undefined,
                owner: vote.ownerInfo && vote.ownerInfo.length > 0 ? {
                    _id: vote.ownerInfo[0]._id.toString(),
                    name: vote.ownerInfo[0].name
                } : undefined,
                updatedBy: vote.updatedBy || undefined,
                createdAt: vote.createdAt.toISOString(),
                updatedAt: vote.updatedAt.toISOString(),
            });
        });

        // Преобразуем в нужный формат и сортируем
        const dailyData: DailyVoteData[] = Object.entries(groupedByDate)
            .map(([date, votes]) => ({
                date,
                count: votes.length,
                votes
            }))
            .sort((a, b) => {
                // Сортируем по дате (новые сверху)
                if (a.date === 'Sana ko\'rsatilmagan') return 1;
                if (b.date === 'Sana ko\'rsatilmagan') return -1;
                return b.date.localeCompare(a.date);
            });

        // Подсчитываем общую статистику
        const totalVotes = dailyData.reduce((sum, day) => sum + day.count, 0);
        const totalDays = dailyData.length;

        return {
            dailyData,
            totalVotes,
            totalDays
        };

    } catch (error) {
        console.error('Error fetching daily votes:', error);
        throw new Error('Kunlik ovozlarni yuklashda xatolik yuz berdi');
    }
}
