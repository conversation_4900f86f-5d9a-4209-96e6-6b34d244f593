'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";
import { CheckPhonesRequest, PhoneCheckResult } from "../model/biriktirish-schema";

/**
 * Проверяет номера телефонов на наличие в базе данных по originalPhoneNumber
 */
export async function checkPhones(request: CheckPhonesRequest): Promise<PhoneCheckResult[]> {
    try {
        await dbConnect();

        const { phoneNumbers } = request;
        const results: PhoneCheckResult[] = [];

        for (const phoneNumber of phoneNumbers) {
            // Очищаем номер от лишних символов
            const cleanNumber = phoneNumber.replace(/[\s\-\(\)\+]/g, '');

            // Ищем по originalPhoneNumber
            // Если номер содержит 6 или больше цифр, ищем по последним 6 цифрам
            let availableVote = null;
            let linkedVote = null;

            if (cleanNumber.length >= 6) {
                // Берем последние 6 цифр и формируем паттерн для поиска
                const lastSixDigits = cleanNumber.slice(-6);
                const searchPattern = `.*${lastSixDigits.slice(0, 2)}-${lastSixDigits.slice(2, 4)}-${lastSixDigits.slice(4, 6)}`;

                // Сначала ищем доступные голоса (без номера телефона)
                availableVote = await VotesSchema.findOne({
                    originalPhoneNumber: { $regex: searchPattern, $options: 'i' },
                    $or: [
                        { phoneNumber: { $exists: false } },
                        { phoneNumber: null },
                        { phoneNumber: '' }
                    ]
                });

                // Если не найден доступный, ищем уже привязанный
                if (!availableVote) {
                    linkedVote = await VotesSchema.findOne({
                        originalPhoneNumber: { $regex: searchPattern, $options: 'i' },
                        $and: [
                            { phoneNumber: { $exists: true } },
                            { phoneNumber: { $ne: null } },
                            { phoneNumber: { $ne: '' } }
                        ]
                    }).populate('ownerId', 'name');
                }
            } else {
                // Если меньше 6 цифр, ищем как есть
                const escapedNumber = cleanNumber.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

                availableVote = await VotesSchema.findOne({
                    originalPhoneNumber: { $regex: escapedNumber, $options: 'i' },
                    $or: [
                        { phoneNumber: { $exists: false } },
                        { phoneNumber: null },
                        { phoneNumber: '' }
                    ]
                });

                if (!availableVote) {
                    linkedVote = await VotesSchema.findOne({
                        originalPhoneNumber: { $regex: escapedNumber, $options: 'i' },
                        $and: [
                            { phoneNumber: { $exists: true } },
                            { phoneNumber: { $ne: null } },
                            { phoneNumber: { $ne: '' } }
                        ]
                    }).populate('ownerId', 'name');
                }
            }

            if (availableVote) {
                // Найден доступный голос
                results.push({
                    phoneNumber,
                    found: true,
                    voteId: availableVote._id.toString(),
                    originalPhoneNumber: availableVote.originalPhoneNumber,
                    isAlreadyLinked: false
                });
            } else if (linkedVote) {
                // Найден уже привязанный голос
                results.push({
                    phoneNumber,
                    found: false,
                    originalPhoneNumber: linkedVote.originalPhoneNumber,
                    isAlreadyLinked: true,
                    ownerName: linkedVote.ownerId?.name || 'Noma\'lum'
                });
            } else {
                // Не найден вообще
                results.push({
                    phoneNumber,
                    found: false,
                    isAlreadyLinked: false
                });
            }
        }

        return results;
    } catch (error) {
        console.error('Error checking phones:', error);
        throw new Error('Failed to check phones');
    }
}
