'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";
import fs from 'fs/promises';
import path from 'path';

export type DeleteVoteParams = {
    voteId: string;
    deletedBy?: string; // Кто удаляет данные
};

export type DeleteVoteResult = {
    success: boolean;
    error?: string;
};

/**
 * Удаляет голос из базы данных и связанный файл изображения
 */
export async function deleteVote(params: DeleteVoteParams): Promise<DeleteVoteResult> {
    try {
        await dbConnect();

        const { voteId, deletedBy } = params;

        // Находим голос для получения imageKey перед удалением
        const vote = await VotesSchema.findById(voteId);
        
        if (!vote) {
            return {
                success: false,
                error: "Ovoz topilmadi"
            };
        }

        // Удаляем файл изображения, если он существует
        if (vote.imageKey) {
            try {
                const imagePath = path.join(process.cwd(), 'public', 'vote-images', vote.imageKey);
                await fs.unlink(imagePath);
                console.log(`Image file deleted: ${imagePath}`);
            } catch (fileError) {
                console.warn(`Could not delete image file: ${vote.imageKey}`, fileError);
                // Не прерываем процесс, если файл не найден
            }
        }

        // Удаляем запись из базы данных
        const deletedVote = await VotesSchema.findByIdAndDelete(voteId);

        if (!deletedVote) {
            return {
                success: false,
                error: "Ovozni o'chirishda xatolik yuz berdi"
            };
        }

        console.log(`Vote deleted by ${deletedBy || 'unknown'}: ${voteId}`);

        return {
            success: true
        };

    } catch (error) {
        console.error('Error deleting vote:', error);
        return {
            success: false,
            error: "Ovozni o'chirishda xatolik yuz berdi"
        };
    }
}
