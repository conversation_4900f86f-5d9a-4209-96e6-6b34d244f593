'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";

export type UpdateVotesParams = {
    voteIds: string[]; // Массив ID голосов для обновления
    ownerId: string; // ID хозяйства, к которому нужно привязать голоса
    updatedBy?: string; // Кто обновляет данные (имя пользователя или ID)
};

/**
 * Обновляет голоса, привязывая их к указанному хозяйству
 */
export async function updateVotes({ voteIds, ownerId, updatedBy }: UpdateVotesParams) {
    try {
        await dbConnect();

        // Создаем объект с данными для обновления
        const updateData: any = { ownerId };

        // Если передан updatedBy, добавляем его в данные для обновления
        if (updatedBy) {
            updateData.updatedBy = updatedBy;
        }

        // Обновляем все голоса, указанные в voteIds
        const result = await VotesSchema.updateMany(
            { _id: { $in: voteIds } },
            { $set: updateData }
        );

        return {
            success: true,
            data: {
                modifiedCount: result.modifiedCount,
                matchedCount: result.matchedCount
            }
        };
    } catch (error) {
        console.error('Error updating votes:', error);
        return {
            success: false,
            error: (error as Error).message || 'Failed to update votes'
        };
    }
}
