"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { MergePhonesRequest, MergePhonesResponse } from "./biriktirish-schema"

/**
 * Хук для объединения номеров телефонов
 */
export function useMergePhones() {
  const queryClient = useQueryClient()
  
  const mutation = useMutation({
    mutationFn: async (data: MergePhonesRequest): Promise<MergePhonesResponse> => {
      const response = await fetch('/api/votes/merge-phones', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Telefon raqamlarini biriktirishda xatolik yuz berdi')
      }

      return response.json()
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(`Muvaffaqiyatli biriktrildi: ${data.data?.mergedCount} ta ovoz`)
        
        // Инвалидируем кеш для обновления данных
        queryClient.invalidateQueries({ queryKey: ["votes"] })
        queryClient.invalidateQueries({ queryKey: ["villagePersonVotes"] })
        queryClient.invalidateQueries({ queryKey: ["villagePeopleList"] })
        queryClient.invalidateQueries({ queryKey: ["votesSelect"] })
        
        return true
      } else {
        toast.error(data.error || "Telefon raqamlarini biriktirishda xatolik yuz berdi")
        return false
      }
    },
    onError: (error) => {
      console.error("Error merging phones:", error)
      toast.error(error.message || "Telefon raqamlarini biriktirishda xatolik yuz berdi")
      return false
    }
  })

  return mutation
}
