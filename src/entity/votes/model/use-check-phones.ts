"use client"

import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"
import { CheckPhonesRequest, CheckPhonesResponse } from "./biriktirish-schema"

/**
 * Хук для проверки номеров телефонов
 */
export function useCheckPhones() {
  const mutation = useMutation({
    mutationFn: async (data: CheckPhonesRequest): Promise<CheckPhonesResponse> => {
      const response = await fetch('/api/votes/check-phones', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Telefon raqamlarini tekshirishda xatolik yuz berdi')
      }

      return response.json()
    },
    onSuccess: (data) => {
      if (data.success) {
        const foundCount = data.data?.filter(result => result.found).length || 0
        const totalCount = data.data?.length || 0
        
        toast.success(`Tekshirish tugallandi: ${foundCount}/${totalCount} ta raqam topildi`)
      } else {
        toast.error(data.error || "Telefon raqamlarini tekshirishda xatolik yuz berdi")
      }
    },
    onError: (error) => {
      console.error("Error checking phones:", error)
      toast.error(error.message || "Telefon raqamlarini tekshirishda xatolik yuz berdi")
    }
  })

  return mutation
}
