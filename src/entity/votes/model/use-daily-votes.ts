"use client"

import { useQuery } from "@tanstack/react-query"
import { toast } from "sonner"
import { getDailyVotes, DailyVotesResult } from "@/entity/votes/api"

/**
 * Хук для получения голосов, сгруппированных по дням
 */
export function useDailyVotes() {
  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery<DailyVotesResult>({
    queryKey: ["dailyVotes"],
    queryFn: async () => {
      try {
        return await getDailyVotes()
      } catch (error) {
        console.error("Error fetching daily votes:", error)
        toast("Xatolik", {
          description: "Kunlik ovozlarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    },
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false
  })

  return {
    dailyData: data?.dailyData || [],
    totalVotes: data?.totalVotes || 0,
    totalDays: data?.totalDays || 0,
    isLoading,
    isError,
    error,
    refetch
  }
}
