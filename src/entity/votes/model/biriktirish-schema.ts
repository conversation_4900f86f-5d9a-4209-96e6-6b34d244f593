import { z } from "zod"

/**
 * Схема для одного номера телефона в форме объединения
 */
export const phoneNumberItemSchema = z.object({
  id: z.string(), // Уникальный ID для каждого инпута
  phoneNumber: z.string()
    .min(1, { message: "Telefon raqamini kiriting" })
    .regex(/^\+?[0-9\s\-\(\)]+$/, { message: "Telefon raqami noto'g'ri formatda" }),
  status: z.enum(['pending', 'found', 'not-found']).default('pending'), // Статус проверки
  voteId: z.string().optional(), // ID найденного голоса
})

/**
 * Схема для формы объединения номеров телефонов
 */
export const biriktirushFormSchema = z.object({
  phoneNumbers: z.array(phoneNumberItemSchema)
    .min(1, { message: "Kamida bitta telefon raqami qo'shing" }),
  selectedPersonId: z.string()
    .min(1, { message: "Odamni tanlang" }),
})

/**
 * Схема для проверки номеров телефонов
 */
export const checkPhonesSchema = z.object({
  phoneNumbers: z.array(z.string())
    .min(1, { message: "Kamida bitta telefon raqami kerak" }),
})

/**
 * Схема для объединения найденных номеров
 */
export const mergePhonesSchema = z.object({
  votes: z.array(z.object({
    voteId: z.string(),
    phoneNumber: z.string()
  }))
    .min(1, { message: "Kamida bitta ovoz topilgan bo'lishi kerak" }),
  ownerId: z.string()
    .min(1, { message: "Egasini tanlang" }),
  updatedBy: z.string().optional(),

})

export type PhoneNumberItem = z.infer<typeof phoneNumberItemSchema>
export type BiriktirushFormValues = z.infer<typeof biriktirushFormSchema>
export type CheckPhonesRequest = z.infer<typeof checkPhonesSchema>
export type MergePhonesRequest = z.infer<typeof mergePhonesSchema>

/**
 * Результат проверки одного номера
 */
export type PhoneCheckResult = {
  phoneNumber: string
  found: boolean
  voteId?: string
  originalPhoneNumber?: string
  isAlreadyLinked?: boolean
  ownerName?: string
}

/**
 * Ответ API для проверки номеров
 */
export type CheckPhonesResponse = {
  success: boolean
  data?: PhoneCheckResult[]
  error?: string
}

/**
 * Ответ API для объединения номеров
 */
export type MergePhonesResponse = {
  success: boolean
  data?: {
    mergedCount: number
    ownerId: string
  }
  error?: string
}

/**
 * Элемент для объединения (голос + номер телефона)
 */
export type VoteToMerge = {
  voteId: string
  phoneNumber: string
}
