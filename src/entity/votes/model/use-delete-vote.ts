"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { deleteVote } from "../api"
import { toast } from "sonner"

/**
 * Хук для удаления голоса
 */
export function useDeleteVote() {
  const queryClient = useQueryClient()
  
  const mutation = useMutation({
    mutationFn: deleteVote,
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Ovoz muvaffaqiyatli o'chirildi")
        
        // Инвалидируем кеш для обновления данных
        queryClient.invalidateQueries({ queryKey: ["votes"] })
        queryClient.invalidateQueries({ queryKey: ["villagePersonVotes"] })
        
        return true
      } else {
        toast.error(data.error || "Ovozni o'chirishda xatolik yuz berdi")
        return false
      }
    },
    onError: (error) => {
      console.error("Error deleting vote:", error)
      toast.error("<PERSON><PERSON><PERSON><PERSON> o'chirishda xatolik yuz berdi")
      return false
    }
  })
  
  return mutation
}
