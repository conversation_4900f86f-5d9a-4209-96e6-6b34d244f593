import { z } from "zod"

/**
 * Схема для формы добавления номера телефона
 */
export const addPhoneSchema = z.object({
  voteId: z.string()
    .min(1, { message: "<PERSON><PERSON>z tanlash kerak" }),
  phoneNumber: z.string().length(3, { message: "Dastlabki 3 ta raqamni kiriting" }),
  ownerId: z.string().optional(),
  updatedBy: z.string().optional()
})

export type AddPhoneFormValues = z.infer<typeof addPhoneSchema>
