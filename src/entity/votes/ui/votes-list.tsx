"use client"

import React, { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/ui/table"
import { SimplePagination } from "@/shared/ui/pagination"
import { Card, CardContent } from "@/shared/ui/card"
import { Button } from "@/shared/ui/button"
import { Phone, Search, Loader2, Trash2 } from "lucide-react"
import { Input } from "@/shared/ui/input"
import { useVotesList } from "../model/use-votes"
import {AddPhoneModal, AddPhoneModalParams} from "./add-phone-modal"
import {DeleteVoteModal, DeleteVoteModalParams} from "./delete-vote-modal"
import {useModalControl} from "@/shared/lib/use-modal-control";
import {VoteList} from "@/entity/votes/api";
import {DialogLazy} from "@/shared/ui/dialog";
import {useIsSuperAdmin} from "@/entity/users/hooks/use-is-super-admin";
import {ConnectedVotesFilter} from "@/features/votes/ui";
import dayjs from 'dayjs'


export function VotesList() {
    // Проверяем, является ли пользователь суперадмином
    const isSuperAdmin = useIsSuperAdmin()

    // Состояние для поискового значения
    const [searchValue, setSearchValue] = useState("")

    // Состояние для модальных окон
    const addPhoneModalControl = useModalControl<AddPhoneModalParams>()
    const deleteVoteModalControl = useModalControl<DeleteVoteModalParams>()

    // Используем кастомный хук для получения списка голосов
    const {
        votes,
        total,
        totalPages,
        currentPage,
        isLoading,
        setParams,
        refetch,
        params
    } = useVotesList({
        page: 1,
        size: 20,
    });

    // Обработчик изменения страницы
    const handlePageChange = (page: number) => {
        setParams(prev => ({
            ...prev,
            page
        }));
    };

    // Обработчик изменения размера страницы
    const handlePageSizeChange = (size: number) => {
        setParams(prev => ({
            ...prev,
            size,
            page: 1
        }));
    };

    // Обработчик изменения поиска
    const handleSearchChange = (inputValue: string) => {
        setSearchValue(inputValue); // Обновляем локальное состояние для отображения

        const cleanValue = inputValue.replace('+', '');

        // Если введено 6 или больше символов, ищем по originalPhoneNumber
        // Иначе ищем по phoneNumber
        if (cleanValue.length >= 6) {
            setParams(prev => ({
                ...prev,
                originalPhoneNumber: cleanValue,
                phoneNumber: undefined, // Очищаем поиск по phoneNumber
                page: 1
            }));
        } else {
            setParams(prev => ({
                ...prev,
                phoneNumber: cleanValue,
                originalPhoneNumber: undefined, // Очищаем поиск по originalPhoneNumber
                page: 1
            }));
        }
    };

    // Обработчик открытия модального окна добавления телефона
    const handleAddPhone = (vote: VoteList) => {
        addPhoneModalControl.openModal({
            voteId: vote._id,
            ownerId: vote.ownerId,
            phoneNumber: vote.phoneNumber,
            originalPhoneNumber: vote.originalPhoneNumber
        });
    };

    // Обработчик открытия модального окна удаления
    const handleDeleteVote = (vote: VoteList) => {
        deleteVoteModalControl.openModal({
            voteId: vote._id,
            phoneNumber: vote.phoneNumber,
            voteDate: vote.voteDate
        });
    };

    return (
        <>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
                <h1 className="text-3xl font-bold">Berilgan ovozlar {!isLoading && <span>({total})</span>}</h1>

                <div className="flex gap-2 w-full md:w-auto">
                    <div className="relative flex-1">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Telefon raqami yoki original raqam bo'yicha qidirish"
                            value={searchValue}
                            onChange={(e) => handleSearchChange(e.target.value)}
                            className="pl-8"
                        />
                    </div>
                    <ConnectedVotesFilter
                        value={typeof params.excludeWithOwner === 'undefined' ? 'none' : params.excludeWithOwner ? 'not_connected' : 'connected'}
                        onValueChange={(value) => {
                            setParams(prev => ({
                                ...prev,
                                excludeWithOwner: value === 'none' ? undefined : value === 'not_connected',
                                page: 1
                            }));
                        }}
                    />
                </div>
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block">
                {isLoading ? (
                    <div className="flex justify-center items-center py-20">
                        <Loader2 className="h-10 w-10 animate-spin text-primary" />
                    </div>
                ) : votes.length === 0 ? (
                    <div className="text-center py-20 bg-gray-50 rounded-lg">
                        <p className="text-gray-500">
                            Ovozlar mavjud emas
                        </p>
                    </div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[80px]">Original raqam</TableHead>
                                <TableHead className="text-center">Biriktirilgan telefon raqami</TableHead>
                                <TableHead>Sana</TableHead>
                                <TableHead>{"Xo'jalik"}</TableHead>
                                    <TableHead>Biriktirilgan sana</TableHead>
                                <TableHead className={`text-right ${isSuperAdmin ? 'w-[140px]' : 'w-[100px]'}`}>Amallar</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {votes.map((vote) => (
                                <TableRow key={vote._id}>
                                    <TableCell>
                                        {vote.originalPhoneNumber}
                                    </TableCell>
                                    <TableCell className="text-center">{vote.phoneNumber || '-'}</TableCell>
                                    <TableCell>{vote.voteDate || '-'}</TableCell>
                                    <TableCell>{vote.owner ? vote.owner.name : '-'}</TableCell>
                                    <TableCell>{vote.owner ? dayjs(vote.updatedAt).format("YYYY-MM-DD HH:mm") : '-'}</TableCell>
                                    <TableCell className="text-right">
                                        <div className="flex gap-1 justify-end">
                                            {
                                                !isSuperAdmin && (vote.ownerId && vote.phoneNumber) ? null : (
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-8 w-8 p-0 cursor-pointer"
                                                        onClick={() => handleAddPhone(vote)}
                                                        title="Nomer qo'shish"
                                                    >
                                                        <Phone className="h-4 w-4" />
                                                    </Button>
                                                )
                                            }
                                            {isSuperAdmin && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-8 w-8 p-0 cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50"
                                                    onClick={() => handleDeleteVote(vote)}
                                                    title="Ovozni o'chirish"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden">
                {isLoading ? (
                    <div className="flex justify-center items-center py-20">
                        <Loader2 className="h-10 w-10 animate-spin text-primary" />
                    </div>
                ) : votes.length === 0 ? (
                    <div className="text-center py-20 bg-gray-50 rounded-lg">
                        <p className="text-gray-500">
                            Ovozlar mavjud emas
                        </p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {votes.map((vote) => (
                            <Card key={vote._id} className="overflow-hidden py-1">
                                <CardContent className="p-4">
                                    <div className="flex justify-between items-start">
                                        <div className="flex items-center gap-3">
                                            {vote.originalPhoneNumber}
                                            <div>
                                                <div className="flex items-center gap-1">
                                                    <Phone className="h-3 w-3 text-gray-500" />
                                                    <span className="text-sm font-medium">{vote.phoneNumber || '-'}</span>
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {vote.voteDate || '-'}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex gap-1">
                                            {
                                                !isSuperAdmin && (vote.ownerId && vote.phoneNumber) ? null : (
                                                  <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-8 w-8 p-0 cursor-pointer"
                                                    onClick={() => handleAddPhone(vote)}
                                                    title="Nomer qo'shish"
                                                  >
                                                      <Phone className="h-4 w-4" />
                                                  </Button>
                                                )
                                            }
                                            {isSuperAdmin && (
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-8 w-8 p-0 cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50"
                                                    onClick={() => handleDeleteVote(vote)}
                                                    title="Ovozni o'chirish"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </div>

            {!isLoading && votes.length > 0 && (
                <SimplePagination
                    currentPage={currentPage}
                    rowsPerPage={20}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                    handleRowsPerPageChange={(value) => handlePageSizeChange(Number(value))}
                    contentLength={votes.length}
                    total={total}
                />
            )}

            <DialogLazy
                open={addPhoneModalControl.visible}
                onOpenChange={addPhoneModalControl.closeModal}
                modal={false}
                content={{
                    className: "sm:max-w-[425px] overflow-visible"
                }}
            >
                {/* Модальное окно для добавления номера */}
                <AddPhoneModal
                    modalControl={addPhoneModalControl}
                    onSuccess={refetch}
                />
            </DialogLazy>

            <DialogLazy
                open={deleteVoteModalControl.visible}
                onOpenChange={deleteVoteModalControl.closeModal}
                modal={false}
                content={{
                    className: "sm:max-w-[425px] overflow-visible"
                }}
            >
                {/* Модальное окно для удаления голоса */}
                <DeleteVoteModal
                    modalControl={deleteVoteModalControl}
                    onSuccess={refetch}
                />
            </DialogLazy>
        </>
    )
}
