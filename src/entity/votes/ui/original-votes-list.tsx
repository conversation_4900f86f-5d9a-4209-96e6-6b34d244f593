"use client"

import React from "react"
import { SimplePagination } from "@/shared/ui/pagination"
import { Input } from "@/shared/ui/input"
import {useOriginalVotesList} from "@/entity/votes/model/use-original-votes";
import {getExternalImageUrl} from "@/shared/lib/external-images";
import {useVoteToken} from "@/entity/votes/model/use-vote-token";


export function OriginalVotesList() {

  const {token, captcha, captchaAns, refreshCaptcha, base64, setCaptchaAns, getToken} = useVoteToken()

  // Используем кастомный хук для получения списка голосов
  const {
    votes,
    total,
    totalPages,
    currentPage,
    isLoading,
    setParams
  } = useOriginalVotesList(token || '');

  // Обработчик изменения страницы
  const handlePageChange = (page: number) => {
    setParams(prev => ({
      ...prev,
      page
    }));
  };

  return (
    <div className="container mx-auto">
      {
        captcha && (
          <div className='container mx-auto my-2 p-5'>
            <h1 className='text-center text-[25px]'>{"Ko'ktol uchun berilgan ovozlarni tekshirish"}</h1>
            <div className='flex center my-5 gap-x-5'>
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img src={base64(captcha.image)} alt=""/>
              <button
                onClick={refreshCaptcha}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-xl shadow-md border border-gray-300 hover:bg-gray-200 active:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 transition"
              >
                🔄 <span>Captchani yangilash</span>
              </button>
            </div>
            <div className='mb-5'>
              <input
                className='w-full px-4 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-400'
                placeholder='Captcha javobini kiriting'
                value={captchaAns}
                onChange={(e) => setCaptchaAns(e.target.value)}
              />
            </div>
            <button
              className="cursor-pointer w-full px-6 py-2 bg-blue-500 text-white font-semibold rounded-xl shadow-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 transition disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
              disabled={!captchaAns}
              onClick={getToken}
            >
              Tokenni olish
            </button>
          </div>
        )
      }

      <div className="flex gap-4 flex-wrap">
        {
          votes?.map(vote => {
            return (
              <div className='flex-1/5 bg-[#c3c3c3]' key={vote.phoneNumber}>
                <p>{vote.phoneNumber}</p>
                <p className='text-center py-2'>{vote.voteDate}</p>
              </div>
            )
          })
        }
      </div>

      {!isLoading && votes.length > 0 && (
        <div className="flex justify-between mt-10">
          <Input placeholder='Sahifa sonini kiriting' value={currentPage} onChange={(e) => handlePageChange(Number(e.target.value))}/>
          <SimplePagination
            currentPage={currentPage}
            rowsPerPage={20}
            totalPages={totalPages}
            handlePageChange={handlePageChange}
            contentLength={votes.length}
            total={total}
          />
        </div>
      )}
    </div>
  )
}

