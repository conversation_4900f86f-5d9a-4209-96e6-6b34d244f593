"use client"

import { useState } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Plus, Trash2, Check, X, AlertCircle, RotateCcw } from "lucide-react"

import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Input } from "@/shared/ui/input"
import { Label } from "@/shared/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card"
import { cn } from "@/shared/lib"

import { useCheckPhones } from "@/entity/votes/model"
import { useMergePhones } from "@/entity/votes/model"
import {
  biriktirushFormSchema,
  BiriktirushFormValues,
  PhoneNumberItem,
  PhoneCheckResult
} from "@/entity/votes/model"
import {nanoid} from "nanoid";
import {VillagePeopleSelect} from "@/entity/village-people/ui";

// Начальные значения формы
const getInitialFormValues = (): BiriktirushFormValues => ({
  phoneNumbers: [{
    id: nanoid(7),
    phoneNumber: "+998",
    status: "pending"
  }],
  selectedPersonId: "",
})

export function BiriktirushForm() {
  const [checkResults, setCheckResults] = useState<PhoneCheckResult[]>([])
  const [isChecked, setIsChecked] = useState(false)

  const checkPhonesMutation = useCheckPhones()
  const mergePhonesMutation = useMergePhones()

  const form = useForm<BiriktirushFormValues>({
    // @ts-ignore
    resolver: zodResolver(biriktirushFormSchema),
    defaultValues: getInitialFormValues(),
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "phoneNumbers",
  })

  // Очистка формы и localStorage
  const clearForm = () => {
    // Сбрасываем состояние
    const initialValues = getInitialFormValues()
    form.reset(initialValues)
    setCheckResults([])
    setIsChecked(false)
  }

  // Добавить новый номер телефона
  const addPhoneNumber = () => {
    append({
      id: nanoid(7),
      phoneNumber: "+998",
      status: "pending"
    })
  }

  // Удалить номер телефона
  const removePhoneNumber = (index: number) => {
    remove(index)
    // Если удаляем после проверки, сбрасываем состояние
    if (isChecked) {
      setIsChecked(false)
      setCheckResults([])
    }
  }

  // Проверить номера телефонов
  const handleCheck = async () => {
    const phoneNumbers = form.getValues("phoneNumbers")
    const phoneNumbersToCheck = phoneNumbers
      .map(item => item.phoneNumber.trim())
      .filter(phone => phone.length > 0)

    if (phoneNumbersToCheck.length === 0) {
      return
    }

    try {
      const result = await checkPhonesMutation.mutateAsync({
        phoneNumbers: phoneNumbersToCheck
      })

      if (result.success && result.data) {
        setCheckResults(result.data)
        setIsChecked(true)

        // Обновляем статусы в форме
        const updatedPhoneNumbers = phoneNumbers.map(item => {
          const checkResult = result.data?.find(r => r.phoneNumber === item.phoneNumber.trim())
          return {
            ...item,
            status: checkResult?.found ? 'found' : 'not-found',
            voteId: checkResult?.voteId
          } as PhoneNumberItem
        })

        form.setValue("phoneNumbers", updatedPhoneNumbers)
      }
    } catch (error) {
      console.error("Error checking phones:", error)
    }
  }

  // Объединить найденные номера
  const handleMerge = async (data: BiriktirushFormValues) => {
    const foundVotes = data.phoneNumbers
      .filter(item => item.status === 'found' && item.voteId)
      .map(item => ({
        voteId: item.voteId!,
        phoneNumber: item.phoneNumber.trim()
      }))

    if (foundVotes.length === 0) {
      return
    }

    try {
      const success = await mergePhonesMutation.mutateAsync({
        votes: foundVotes,
        ownerId: data.selectedPersonId,
      })

      if (success) {
        // Очищаем форму и localStorage после успешного объединения
        clearForm()
      }
    } catch (error) {
      console.error("Error merging phones:", error)
    }
  }

  const foundCount = checkResults.filter(r => r.found).length
  const linkedCount = checkResults.filter(r => r.isAlreadyLinked).length
  const notFoundCount = checkResults.filter(r => !r.found && !r.isAlreadyLinked).length
  const hasFoundNumbers = foundCount > 0

  return (
    <Card className="w-full max-w-4xl mt-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Telefon raqamlarini biriktirish</CardTitle>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={clearForm}
            disabled={checkPhonesMutation.isPending || mergePhonesMutation.isPending}
            className="text-red-600 hover:text-red-700"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Formani tozalash
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 mx-0">
        {/* @ts-ignore */}
        <form onSubmit={form.handleSubmit(handleMerge)} className="space-y-6">
          {/* Выбор человека */}
          <div className="space-y-2">
            <Label htmlFor="selectedPersonId">Odamni tanlang *</Label>
            <VillagePeopleSelect
              value={form.watch("selectedPersonId")}
              onValueChange={(value) => {
                form.setValue("selectedPersonId", value)
              }}
              placeholder="Odamni qidiring..."
              emptyMessage="Hech kim topilmadi"
            />
            {form.formState.errors.selectedPersonId && (
              <p className="text-sm text-red-500">
                {form.formState.errors.selectedPersonId.message}
              </p>
            )}
          </div>

          {/* Номера телефонов */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Telefon raqamlari *</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addPhoneNumber}
                disabled={checkPhonesMutation.isPending || mergePhonesMutation.isPending}
              >
                <Plus className="h-4 w-4 mr-2" />
                Qo'shish
              </Button>
            </div>

            <div className="space-y-3">
              {fields.map((field, index) => {
                const phoneNumber = form.watch(`phoneNumbers.${index}.phoneNumber`)
                const status = form.watch(`phoneNumbers.${index}.status`)
                const checkResult = checkResults.find(r => r.phoneNumber === phoneNumber?.trim())

                return (
                  <div key={field.id} className="flex items-center gap-3">
                    <div className="flex-1">
                      <div className="relative">
                        <Input
                          {...form.register(`phoneNumbers.${index}.phoneNumber`)}
                          placeholder="+998 90 123 45 67"
                          className={cn(
                            "pr-10",
                            isChecked && status === 'found' && "border-green-500 bg-green-50",
                            isChecked && status === 'not-found' && !checkResult?.isAlreadyLinked && "border-red-500 bg-red-50",
                            isChecked && checkResult?.isAlreadyLinked && "border-orange-500 bg-orange-50"
                          )}
                          disabled={checkPhonesMutation.isPending || mergePhonesMutation.isPending}
                          onChange={(e) => {
                            form.setValue(`phoneNumbers.${index}.phoneNumber`, e.target.value)
                          }}
                        />
                        {isChecked && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            {status === 'found' ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : status === 'not-found' && checkResult?.isAlreadyLinked ? (
                              <AlertCircle className="h-4 w-4 text-orange-500" />
                            ) : status === 'not-found' ? (
                              <X className="h-4 w-4 text-red-500" />
                            ) : null}
                          </div>
                        )}
                      </div>
                      {isChecked && checkResult && (
                        <p className={cn(
                          "text-xs mt-1",
                          checkResult.found ? "text-green-600" :
                          checkResult.isAlreadyLinked ? "text-orange-600" : "text-red-600"
                        )}>
                          {checkResult.found
                            ? `Topildi: ${checkResult.originalPhoneNumber} → ${phoneNumber} saqlanadi`
                            : checkResult.isAlreadyLinked
                            ? `Nomer biriktirilgan: ${checkResult.ownerName}`
                            : "Topilmadi"
                          }
                        </p>
                      )}
                    </div>

                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removePhoneNumber(index)}
                        disabled={checkPhonesMutation.isPending || mergePhonesMutation.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                )
              })}
            </div>

            {form.formState.errors.phoneNumbers && (
              <p className="text-sm text-red-500">
                {form.formState.errors.phoneNumbers.message}
              </p>
            )}
          </div>

          {/* Кнопки действий */}
          <div className="flex gap-3">
            {!isChecked ? (
              <Button
                type="button"
                onClick={handleCheck}
                disabled={checkPhonesMutation.isPending || mergePhonesMutation.isPending}
                className="min-w-[150px]"
              >
                {checkPhonesMutation.isPending ? "Tekshirilmoqda..." : "Tekshirish"}
              </Button>
            ) : (
              <>
                <Button
                  type="submit"
                  disabled={!hasFoundNumbers || mergePhonesMutation.isPending || !form.watch("selectedPersonId")}
                  className="min-w-[200px]"
                >
                  {mergePhonesMutation.isPending
                    ? "Biriktirilmoqda..."
                    : `Topilganlarini biriktirish (${foundCount})`
                  }
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsChecked(false)
                    setCheckResults([])
                    // Сбрасываем только статусы, но сохраняем номера
                    const updatedPhoneNumbers = form.getValues("phoneNumbers").map(item => ({
                      ...item,
                      status: "pending" as const,
                      voteId: undefined
                    }))
                    form.setValue("phoneNumbers", updatedPhoneNumbers)
                  }}
                  disabled={mergePhonesMutation.isPending}
                >
                  Qayta tekshirish
                </Button>
              </>
            )}
          </div>

          {/* Статистика */}
          {isChecked && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Tekshirish natijalari:</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span>Topildi: {foundCount}</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                  <span>Biriktirilgan: {linkedCount}</span>
                </div>
                <div className="flex items-center gap-2">
                  <X className="h-4 w-4 text-red-500" />
                  <span>Topilmadi: {notFoundCount}</span>
                </div>
              </div>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
