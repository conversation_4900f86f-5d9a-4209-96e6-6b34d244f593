"use client"

import React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON> } from "@/shared/ui/dialog"
import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Loader2, AlertTriangle } from "lucide-react"
import { useDeleteVote } from "../model/use-delete-vote"
import { ModalControlType } from "@/shared/lib/use-modal-control"
import { useCurrentUser } from "@/entity/users/hooks/use-current-user"

export type DeleteVoteModalParams = {
    voteId: string;
    phoneNumber?: string;
    voteDate?: string;
}

type DeleteVoteModalProps = {
    onSuccess?: () => void;
    modalControl: ModalControlType<DeleteVoteModalParams>
}

export function DeleteVoteModal({ onSuccess, modalControl }: DeleteVoteModalProps) {
    const currentUser = useCurrentUser()
    const deleteVoteMutation = useDeleteVote()

    const { voteId, phoneNumber, voteDate } = modalControl.modalProps

    // Обработчик удаления
    const handleDelete = async () => {
        const result = await deleteVoteMutation.mutateAsync({
            voteId,
            deletedBy: currentUser._id
        })

        if (result) {
            modalControl.closeModal()
            onSuccess?.()
        }
    }

    return (
        <>
            <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                    Ovozni o'chirish
                </DialogTitle>
            </DialogHeader>

            <div className="space-y-4 my-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-sm text-red-800">
                        <strong>Diqqat!</strong> Bu amal qaytarib bo'lmaydi. Ovoz va unga tegishli barcha ma'lumotlar butunlay o'chiriladi.
                    </p>
                </div>

                <div className="space-y-2">
                    <p className="text-sm text-gray-600">
                        Quyidagi ovozni o'chirishni tasdiqlaysizmi?
                    </p>
                    
                    <div className="bg-gray-50 rounded-lg p-3 space-y-1">
                        {phoneNumber && (
                            <p className="text-sm">
                                <span className="font-medium">Telefon:</span> {phoneNumber}
                            </p>
                        )}
                        {voteDate && (
                            <p className="text-sm">
                                <span className="font-medium">Sana:</span> {voteDate}
                            </p>
                        )}
                        <p className="text-sm">
                            <span className="font-medium">ID:</span> {voteId}
                        </p>
                    </div>
                </div>
            </div>

            <DialogFooter>
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => modalControl.closeModal()}
                    disabled={deleteVoteMutation.isPending}
                >
                    Bekor qilish
                </Button>
                <Button
                    type="button"
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={deleteVoteMutation.isPending}
                >
                    {deleteVoteMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    O'chirish
                </Button>
            </DialogFooter>
        </>
    )
}
