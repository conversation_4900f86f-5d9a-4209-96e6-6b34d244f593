"use client"

import Image from "next/image"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/ui/table"
import { SimplePagination } from "@/shared/ui/pagination"
import { Card, CardContent } from "@/shared/ui/card"
import {VoteList, VotesListParams} from "@/entity/votes/api";
import {PaginationList} from "@/shared/api/external";
import {useQueryParams} from "@/shared/lib/use-query-params";


type Props = {
    votes: PaginationList<VoteList>
}

export const Voices = ({votes}: Props) => {

    const {queryParams, setQueryParams} = useQueryParams<VotesListParams>()
    const currentPage = Number(queryParams.page || '1')
    const rowsPerPage = Number(queryParams.size || '20')

    const currentItems = votes.data

    // Handle page change
    const handlePageChange = (page: number) => {
        setQueryParams({
            page: page.toString()
        })
    }

    const handleRowsPerPageChange = (value: string) => {
        setQueryParams({
            page: '1',
            size: value
        })
    }

    return (
        <>
            <h1 className="text-3xl font-bold mb-8 text-center">Berilgan ovozlar</h1>

            {/* Desktop Table View */}
            <div className="hidden md:block">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[80px]">Image</TableHead>
                            <TableHead>Short Number</TableHead>
                            <TableHead>Full Number</TableHead>
                            <TableHead>Name</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <>
                            {currentItems.map((voice) => (
                                <TableRow key={voice._id}>
                                    <TableCell>
                                        {voice.originalPhoneNumber}
                                    </TableCell>
                                    <TableCell>{'-'}</TableCell>
                                    <TableCell>{'-'}</TableCell>
                                    <TableCell>{'-'}</TableCell>
                                </TableRow>
                            ))}
                        </>
                    </TableBody>
                </Table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
                {currentItems.map((voice) => (
                    <Card key={voice._id} className="overflow-hidden py-1">
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-4">
                                {voice.originalPhoneNumber}
                                <div className="flex-1">
                                    <h3 className="font-medium">{'-'}</h3>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Short: {'-'}</p>
                                        <p>{'-'}</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
            <SimplePagination
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                totalPages={votes.totalPages}
                handlePageChange={handlePageChange}
                handleRowsPerPageChange={handleRowsPerPageChange}
                contentLength={votes.data.length}
                total={votes.total}
            />
        </>
    )
}

export * from './biriktirish-form'

