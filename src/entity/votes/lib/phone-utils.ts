/**
 * Утилиты для работы с номерами телефонов
 */

/**
 * Извлекает последние 6 цифр из originalPhoneNumber формата **-*89-44-45
 * @param originalPhoneNumber - номер в формате **-*89-44-45
 * @returns строка из 6 цифр или null если не удалось извлечь
 */
export function extractLast6Digits(originalPhoneNumber: string): string | null {
  if (!originalPhoneNumber) return null;
  
  // Убираем все символы кроме цифр
  const digits = originalPhoneNumber.replace(/\D/g, '');
  
  // Возвращаем последние 6 цифр
  if (digits.length >= 6) {
    return digits.slice(-6);
  }
  
  return null;
}

/**
 * Извлекает последние 6 цифр из полного номера телефона
 * @param phoneNumber - полный номер телефона
 * @returns строка из 6 цифр или null если не удалось извлечь
 */
export function extractLast6DigitsFromPhone(phoneNumber: string): string | null {
  if (!phoneNumber) return null;
  
  // Убираем все символы кроме цифр
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Возвращаем последние 6 цифр
  if (digits.length >= 6) {
    return digits.slice(-6);
  }
  
  return null;
}

/**
 * Проверяет, совпадают ли последние 6 цифр phoneNumber с цифрами в originalPhoneNumber
 * @param phoneNumber - полный номер телефона
 * @param originalPhoneNumber - оригинальный номер в формате **-*89-44-45
 * @returns true если совпадают, false если нет
 */
export function validatePhoneMatch(phoneNumber: string, originalPhoneNumber: string): boolean {
  const phoneDigits = extractLast6DigitsFromPhone(phoneNumber);
  const originalDigits = extractLast6Digits(originalPhoneNumber);
  
  if (!phoneDigits || !originalDigits) return false;
  
  return phoneDigits === originalDigits;
}

/**
 * Форматирует 6 цифр в формат для поиска в originalPhoneNumber
 * @param digits - 6 цифр
 * @returns регулярное выражение для поиска
 */
export function formatDigitsForSearch(digits: string): string {
  if (digits.length !== 6) return '';
  
  // Формат: **-*89-44-45, где 89-44-45 это последние 6 цифр
  return `.*${digits.slice(0, 2)}-${digits.slice(2, 4)}-${digits.slice(4, 6)}$`;
}
