import { Schema, model, models, Types } from "mongoose";

const Votes = new Schema({
    originalPhoneNumber: { type: String, required: true },
    voteDate: String,
    phoneNumber: { type: String, required: false, unique: true }, // Номер телефона голосующего
    ownerId: { type: Types.ObjectId, ref: 'villagePeople', required: false }, // ID хозяйства, к которому привязан голос
    updatedBy: { type: String, required: false }, // Кто обновил данные (имя пользователя или ID)
}, { timestamps: true });

export const VotesSchema = models?.Votes || model("Votes", Votes);