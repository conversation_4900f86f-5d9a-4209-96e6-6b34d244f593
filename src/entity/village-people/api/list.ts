'use server';

import {dbConnect} from "@/shared/lib/database";
import {VillagePeopleSchema} from "@/entity/village-people/schema";
import {PaginationList} from "@/shared/api/external";

export type TVillagePeopleListParams = {
    page?: number;
    size?: number;
    search?: string;
    planFilter?: string; // Фильтр по плану
    sortByVotes?: 'asc' | 'desc' | null;
}

export type IVillagePeopleListModel = {
    _id: string;
    index: number;
    name: string;
    votesCount: number; // Количество голосов (теперь всегда присутствует)
}

export async function getVillagePeopleListPaginated (params: TVillagePeopleListParams) {
    const { page = 1, size = 20, search = '', planFilter, sortByVotes = null } = params;

    await dbConnect()

    const skip = (page - 1) * size;

    // Создаем базовый фильтр для поиска
    // Экранируем специальные символы регулярных выражений
    const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const searchRegex = new RegExp(escapedSearch, 'i');
    const baseFilter = search
        ? {
            $or: [
                { name: searchRegex },
                { phone: searchRegex },
                { mahalla: searchRegex },
                { street: searchRegex },
            ],
        }
        : {};

    // Всегда используем агрегацию для подсчета голосов
    const aggregationPipeline: any = [
        // Сначала применяем базовый фильтр
        { $match: baseFilter },

        // Присоединяем голоса и подсчитываем их количество
        {
            $lookup: {
                from: 'votes',
                localField: '_id',
                foreignField: 'ownerId',
                as: 'votes'
            }
        },

        // Добавляем поле с количеством голосов
        {
            $addFields: {
                votesCount: { $size: '$votes' }
            }
        },
    ];

    // Если есть фильтр по плану, добавляем дополнительную фильтрацию
    if (planFilter) {
        aggregationPipeline.push({
            $match: planFilter === 'completed'
                ? { votesCount: { $gte: 50 } }
                : { votesCount: { $lt: 50 } }
        });
    }

    const sortStage: any = {};
    if (sortByVotes === 'asc') {
        sortStage.votesCount = 1;
    } else if (sortByVotes === 'desc') {
        sortStage.votesCount = -1;
    } else {
        sortStage.name = 1; // сортировка по имени по умолчанию
    }

    // Добавляем сортировку и пагинацию
    aggregationPipeline.push(
        // Сортируем
        { $sort: sortStage },

        // Добавляем пагинацию
        {
            $facet: {
                data: [
                    { $skip: skip },
                    { $limit: size }
                ],
                totalCount: [
                    { $count: "count" }
                ]
            }
        }
    );

    const [result] = await VillagePeopleSchema.aggregate(aggregationPipeline as any);
    const dataRaw = result.data || [];
    const total = result.totalCount[0]?.count || 0;

    const data = dataRaw.map((doc: any) => ({
        _id: doc._id.toString() as string,
        index: doc.index,
        name: doc.name,
        phone: doc.phone,
        mahalla: doc.mahalla,
        street: doc.street,
        votesCount: doc.votesCount,
    })) as IVillagePeopleListModel[]

    return {
        data,
        total: total as number,
        totalPages: Math.ceil(total / size),
        currentPage: page as number
    } as PaginationList<IVillagePeopleListModel>
}

export async function getVillagePeopleList (searchParams: Promise<TVillagePeopleListParams>) {
    const params = await searchParams
    return getVillagePeopleListPaginated(params)
}