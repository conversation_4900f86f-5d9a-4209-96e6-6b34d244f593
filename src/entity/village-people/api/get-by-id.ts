'use server';

import { dbConnect } from "@/shared/lib/database";
import { VillagePeopleSchema } from "@/entity/village-people/schema";

export type VillagePeopleDetailModel = {
    _id: string;
    index: number;
    name: string;
    createdAt?: string;
    updatedAt?: string;
}

export async function getVillagePeopleById(id: string): Promise<VillagePeopleDetailModel | null> {
    try {
        await dbConnect();

        const villagePerson = await VillagePeopleSchema.findById(id).lean();

        if (!villagePerson) {
            return null;
        }

        return {
            _id: villagePerson._id.toString(),
            index: villagePerson.index,
            name: villagePerson.name,
            createdAt: villagePerson.createdAt?.toISOString(),
            updatedAt: villagePerson.updatedAt?.toISOString(),
        };
    } catch (error) {
        console.error('Error fetching village person by ID:', error);
        return null;
    }
}
