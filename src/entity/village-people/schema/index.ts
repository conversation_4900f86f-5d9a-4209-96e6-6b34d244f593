import {Schema, Document, model, models, Model} from 'mongoose';

export type IVillagePeople = Document & {
    _id: string;
    index: number;
    name: string;
    updatedAt: Date;
    createdAt: Date;
}

const VillagePeople: Schema = new Schema({
    index: { type: Number, required: true },
    name: { type: String, required: true },
}, {
    collection: 'villagePeople',
    timestamps: true
});

export const VillagePeopleSchema = (models.villagePeople || model('villagePeople', VillagePeople)) as Model<IVillagePeople>
