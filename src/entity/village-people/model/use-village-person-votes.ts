"use client"

import { useQuery } from "@tanstack/react-query"
import {getVotesByOwner} from "@/entity/votes/api"
import {toast} from "sonner";

/**
 * Хук для получения голосов, привязанных к хозяйству
 */
export function useVillagePersonVotes(villagePersonId: string) {

  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ["villagePersonVotes", villagePersonId],
    queryFn: async () => {
      try {
        return await getVotesByOwner(villagePersonId)
      } catch (error) {
        console.error("Error fetching village person votes:", error)
        toast("Xatolik", {
          description: "Ovozlarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    },
    enabled: !!villagePersonId
  })

  return {
    votes: data || [],
    total: data?.length || 0,
    isLoading,
    isError,
    error,
    refetch
  }
}
