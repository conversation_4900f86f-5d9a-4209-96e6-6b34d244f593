import { getMoneyByOwner } from "@/entity/money/api"
import { useQuery } from "@tanstack/react-query"
import { toast } from "sonner"

/**
 * Хук для получения голосов, привязанных к хозяйству
 */
export function useVillagePersonMoney(villagePersonId: string) {

  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ["villagePersonMoney", villagePersonId],
    queryFn: async () => {
      try {
        return await getMoneyByOwner(villagePersonId)
      } catch (error) {
        console.error("Error fetching village person money:", error)
        toast("Xatolik", {
          description: "Pullarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    },
    enabled: !!villagePersonId
  })

  return {
    votes: data || [],
    total: data?.length || 0,
    isLoading,
    isError,
    error,
    refetch
  }
}