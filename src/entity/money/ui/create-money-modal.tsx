import { ModalControlType } from "@/shared/lib/use-modal-control"
import { zodResolver } from "@hookform/resolvers/zod"
import { createMoneySchema, TCreateMoneyParams } from "../api/create"
import { useForm } from "react-hook-form"


type Props = {
    modalControl: ModalControlType
}

export const CreateMoney = (props: Props) => {

    const {modalControl} = props

        // Используем react-hook-form с zod для валидации
        const {
            register,
            handleSubmit,
            setValue,
            watch,
            reset,
            formState: { errors, isSubmitting }
        } = useForm<TCreateMoneyParams>({
            resolver: zodResolver(createMoneySchema),
            defaultValues: {
                summa: 0
            }
        })

    return (
        <div>

        </div>
    )
}