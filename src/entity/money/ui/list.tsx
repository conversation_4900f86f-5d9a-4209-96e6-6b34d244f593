"use client";

import { Loader2, Plus } from "lucide-react"
import { useMoneyList } from "../models/use-money"
import { TableBody, TableCell, TableHead, TableHeader, TableRow, Table } from "@/shared/ui/table"
import dayjs from "dayjs"
import { VillagePeopleSelect } from "@/entity/village-people/ui"
import { SimplePagination } from "@/shared/ui/pagination"
import { Button } from "@/shared/ui/button";

export const MoneyList = () => {
    const {
        moneyList,
        isLoading,
        queryParams,
        setQueryParams,
        total,
        currentPage,
        totalPages
    } = useMoneyList()


    // Обработчик изменения страницы
    const handlePageChange = (page: number) => {
        setQueryParams({ page });
    };

    // Обработчик изменения размера страницы
    const handlePageSizeChange = (size: number) => {
        setQueryParams({
            page: 1,
            size
        })
    };


    if (isLoading) {
        return (
            <div className="flex justify-center items-center py-20">
                <Loader2 className="h-10 w-10 animate-spin text-primary" />
            </div>
        )
    }

    return (
        <div>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
                <h1 className="text-2xl font-bold w-[25%]">Ovozlarni biriktirish {!isLoading && <span>({total})</span>}</h1>
                <Button
                    // onClick={() => setCreateModalOpen(true)}
                    className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
                >
                    <Plus className="h-4 w-4" />
                    {"Pul qo'shish"}
                </Button>

            </div>
            <div className="flex flex-wrap gap-2 w-full md:w-[75%] justify-end my-3">
                <VillagePeopleSelect
                    value={queryParams.ownerId}
                    onValueChange={ownerId => setQueryParams({ ownerId })}
                    placeholder="Xo'jalikni tanlang"
                    width="max-w-[350px] w-full"
                />
            </div>
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[80px]">{"Xo'jalik"}</TableHead>
                        <TableHead className="text-center">Summa</TableHead>
                        <TableHead>Sana</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {moneyList.map((money) => (
                        <TableRow key={money._id}>
                            <TableCell>{money.owner ? money.owner.name : '-'}</TableCell>
                            <TableCell className="text-center">{money.summa || '-'}</TableCell>
                            <TableCell>{dayjs(money.createdAt).format("YYYY-MM-DD HH:mm")}</TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
            {!isLoading && moneyList.length > 0 && (
                <SimplePagination
                    currentPage={currentPage}
                    rowsPerPage={queryParams.size || 20}
                    totalPages={totalPages}
                    handlePageChange={handlePageChange}
                    handleRowsPerPageChange={(value) => handlePageSizeChange(Number(value))}
                    contentLength={moneyList.length}
                    total={total}
                />
            )}
        </div>
    )
}