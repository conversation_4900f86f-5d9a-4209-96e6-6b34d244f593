"use server";

import { dbConnect } from "@/shared/lib/database";
import { MoneySchema } from "../schema";

export type MoneyListParams = {
    page?: number;
    size?: number;
    ownerId?: string;
}

export type MoneyListModel = {
    _id: string;
    ownerId: string;
    owner: {
        _id: string;
        name: string;
    }
    summa: number;
    updatedBy?: string;
    createdAt: string;
    updatedAt: string;
}


export async function getMoneyList (params: MoneyListParams) {
    const {page = 1, size = 20, ownerId} = params

    await dbConnect();

    const skip = (page - 1) * size;

    const filter: any = {}

    if(ownerId){
        filter.ownerId = ownerId
    }

    // Создаем агрегационный пайплайн для получения данных с информацией о владельце
    const aggregationPipeline = [
        { $match: filter },
        {
            $lookup: {
                from: 'villagePeople',
                localField: 'ownerId',
                foreignField: '_id',
                as: 'ownerInfo'
            }
        },
        { $sort: { voteDate: -1 } },
        {
            $facet: {
                data: [
                    { $skip: skip },
                    { $limit: size || 20 }
                ],
                totalCount: [
                    { $count: "count" }
                ]
            }
        }
    ];

        const [result] = await MoneySchema.aggregate(aggregationPipeline as any);
        const dataRaw = result.data || [];
        const total = result.totalCount[0]?.count || 0;

        // Приводим результат к типу Vote[]
            // eslint-disable-next-line
            // @ts-ignore
            const data = dataRaw.map((doc) => ({
                _id: doc._id?.toString(),
                summa: doc.summa,
                ownerId: doc.ownerId ? doc.ownerId.toString() : undefined,
                owner: doc.ownerInfo && doc.ownerInfo.length > 0 ? {
                    _id: doc.ownerInfo[0]._id.toString(),
                    name: doc.ownerInfo[0].name
                } : undefined,
                updatedBy: doc.updatedBy || undefined,
                createdAt: doc.createdAt.toISOString(),
                updatedAt: doc.updatedAt.toISOString(),
            })) as MoneyListModel[];
        
            return {
                data,
                total: total as number,
                totalPages: Math.ceil(total / size),
                currentPage: page as number,
            };

}

export async function getMoneyByOwner (ownerId: string) {
    if(!ownerId){
        return []
    }

     const dataRaw = await MoneySchema.find({ownerId}).lean()

     return dataRaw.map((doc) => ({
                _id: doc._id?.toString(),
                summa: doc.summa,
                ownerId: doc.ownerId ? doc.ownerId.toString() : undefined,
                owner: doc.ownerInfo && doc.ownerInfo.length > 0 ? {
                    _id: doc.ownerInfo[0]._id.toString(),
                    name: doc.ownerInfo[0].name
                } : undefined,
                updatedBy: doc.updatedBy || undefined,
                createdAt: doc.createdAt.toISOString(),
                updatedAt: doc.updatedAt.toISOString(),
            })) as MoneyListModel[];
}