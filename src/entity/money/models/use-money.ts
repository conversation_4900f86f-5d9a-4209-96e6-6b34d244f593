"use client"

import { toast } from "sonner"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { getVotesPaginated, updateVotes } from "@/entity/votes/api"
import { useState } from "react"
import { useDebounce } from "@/shared/lib/use-debounce"
import { getMoneyList, MoneyListParams } from "../api"
import { useQueryParams } from "@/shared/lib/use-query-params"
import { useIsSuperAdmin } from "@/entity/users/hooks/use-is-super-admin"

/**
 * Хук для получения списка голосов
 */
export function useMoneyList() {
  const isSuperAdmin = useIsSuperAdmin()
  const {queryParams, setQueryParams} = useQueryParams<MoneyListParams>()

  // Запрос данных с использованием react-query
  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ["money", queryParams],
    queryFn: async () => {
      try {
        return await getMoneyList(queryParams)
      } catch (error) {
        console.error("Error fetching votes:", error)
        toast("Xatolik", {
          description: "Pullarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    },
    enabled: isSuperAdmin
  })

  return {
    moneyList: data?.data || [],
    total: data?.total || 0,
    totalPages: data?.totalPages || 0,
    currentPage: data?.currentPage || 1,
    isLoading,
    isError,
    error,
    refetch,
    setQueryParams,
    queryParams
  }
}