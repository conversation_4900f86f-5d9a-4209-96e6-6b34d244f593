import { Schema, model, models, Types } from "mongoose";

const Money = new Schema({
    summa: { type: Number, required: true },
    ownerId: { type: Types.ObjectId, ref: 'villagePeople' }, // ID хозяйства, к которому привязан голос
    updatedBy: { type: String, required: false }, // Кто обновил данные (имя пользователя или ID)
}, { timestamps: true });

export const MoneySchema = models?.Money || model("Money", Money);