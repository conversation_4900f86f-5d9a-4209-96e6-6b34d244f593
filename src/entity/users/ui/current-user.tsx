'use client';

import {User} from "@/entity/users/model";
import {createContext, ReactNode, useEffect, useState} from "react";

type Props = {
    children?: ReactNode;
}

export const CurrentUserContext = createContext<User | null>(null)

export const CurrentUserProvider = ({ children}: Props) => {
    const [u, setU] = useState<User | null>(null)

    useEffect(() => {
        fetch('/api/me', {
            credentials: "include"
        })
          .then(res => res.json())
          .then(data => {
            setU(data.user)
          })
          .catch(err => {
            console.error(err)
          })
    }, []);

    if(!u){
        return <h1>
            Loading...
        </h1>
    }

    return (
        <CurrentUserContext.Provider value={u}>
            {children}
        </CurrentUserContext.Provider>
    )
}