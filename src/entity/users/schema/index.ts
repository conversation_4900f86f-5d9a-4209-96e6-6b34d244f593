import {Schema, model, models, Model} from "mongoose";

const User = new Schema({
    name: String,
    email: { type: String, unique: true },
    password: {type: String, required: true},
    role: String,
}, { timestamps: true });

export const UserSchema = models?.User || model("User", User);

export type CandidateModel = {
    uid: string
    name: string
    email: string
    passwordHash: string
}

const CandidateSchema = new Schema({
    uid: {type: String, required: true, unique: true},
    email: {type: String, required: true},
    name: {type: String, required: true},
    passwordHash: { type: String, required: true },
})

export const Candidate: Model<CandidateModel> = models?.Candidate || model("Candidate", CandidateSchema);