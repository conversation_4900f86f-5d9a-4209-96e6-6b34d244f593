'use server';

import {User} from "@/entity/users/model";

export type GetMeModel = {
    success: boolean,
    error?: string;
    user?: User
}

export async function getMeMiddleware(token: string): Promise<GetMeModel> {
    if (!token) {
        return {
            success: false,
            error: 'User not found!',
        }
    }

    // const decoded = await verifyJwt(token);
    //
    // if (!decoded) {
    //     return {
    //         success: false,
    //         error: 'User not found!',
    //     }
    // }
    //
    // await dbConnect();
    //
    // // eslint-disable-next-line
    // const user = await UserSchema.findById((decoded as any).userId).select('-password');
    //
    // if (!user) {
    //     return {
    //         success: false,
    //         error: 'User not found!',
    //     }
    // }

    return {
        success: true,
        error: '',
    }
}

export async function getMe () {
    // const cks = await cookies()
    return getMeMiddleware('')
}