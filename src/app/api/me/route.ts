// app/api/users/route.ts

import {NextRequest, NextResponse} from 'next/server';
import { verifyJwt } from '@/entity/users/api/jwt';
import { dbConnect } from '@/shared/lib/database';
import { UserSchema } from '@/entity/users/schema';

export const config = {
    runtime: 'nodejs', // Указываем, что API должен работать в Node.js runtime
};

export async function GET(req: NextRequest) {
    const token = req.cookies.get('token')

    if (!token || !token.value) {
        return NextResponse.json({
            success: false,
            error: 'User not found!',
        }, { status: 400 });
    }

    const decoded = await verifyJwt(token.value);

    if (!decoded) {
        return NextResponse.json({
            success: false,
            error: 'User not found!',
        }, { status: 400 });
    }

    await dbConnect();

    // eslint-disable-next-line
    const user = await UserSchema.findById((decoded as any).userId).select('-password');

    if (!user) {
        return NextResponse.json({
            success: false,
            error: 'User not found!',
        }, { status: 404 });
    }

    return NextResponse.json({
        success: true,
        user,
    });
}
