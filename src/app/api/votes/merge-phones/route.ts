import { NextRequest, NextResponse } from "next/server";
import { mergePhones } from "@/entity/votes/api/merge-phones";
import { mergePhonesSchema } from "@/entity/votes/model/biriktirish-schema";

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        
        // Валидируем входные данные
        const validatedData = mergePhonesSchema.parse(body);
        
        // Объединяем номера телефонов
        const result = await mergePhones(validatedData);
        
        if (result.success) {
            return NextResponse.json(result);
        } else {
            return NextResponse.json(result, { status: 400 });
        }
    } catch (error) {
        console.error('Error in merge-phones API:', error);
        
        if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
            return NextResponse.json({
                success: false,
                error: 'Noto\'g\'ri ma\'lumotlar yuborildi'
            }, { status: 400 });
        }
        
        return NextResponse.json({
            success: false,
            error: 'Telefon raqamlarini biriktirishda xatolik yuz berdi'
        }, { status: 500 });
    }
}
