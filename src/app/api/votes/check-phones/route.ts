import { NextRequest, NextResponse } from "next/server";
import { checkPhones } from "@/entity/votes/api/check-phones";
import { checkPhonesSchema } from "@/entity/votes/model/biriktirish-schema";

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        
        // Валидируем входные данные
        const validatedData = checkPhonesSchema.parse(body);
        
        // Проверяем номера телефонов
        const results = await checkPhones(validatedData);
        
        return NextResponse.json({
            success: true,
            data: results
        });
    } catch (error) {
        console.error('Error in check-phones API:', error);
        
        if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
            return NextResponse.json({
                success: false,
                error: 'Noto\'g\'ri ma\'lumotlar yuborildi'
            }, { status: 400 });
        }
        
        return NextResponse.json({
            success: false,
            error: 'Telefon raqamlarini tekshirishda xatolik yuz berdi'
        }, { status: 500 });
    }
}
