"use client"

import { BiriktirushForm } from "@/entity/votes/ui/biriktirish-form"
import {useIsSuperAdmin} from "@/entity/users/hooks/use-is-super-admin";

export default function BiriktirushPage() {
  const isSuperAdmin = useIsSuperAdmin()

  if (!isSuperAdmin) {
    return <div>Sahifa topilmadi</div>
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Biriktirish</h1>
          <p className="text-muted-foreground">
            Telefon raqamlarini tekshiring va topilganlarini bitta odamga biriktiring.
            Kiritilgan telefon raqamlari ham saqlanadi.
          </p>
        </div>
      </div>

      <BiriktirushForm />
    </div>
  )
}
