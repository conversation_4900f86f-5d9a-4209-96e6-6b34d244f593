'use server';

import {ReactNode} from "react";
import {CurrentUserProvider} from "@/entity/users/ui/current-user";
import {Layout} from "@/shared/ui/layout";
import {AppSidebar} from "@/widgets/sidebar/ui";


type Props = {
    children?: ReactNode;
}

export default async function DashboardLayout ({children}: Props) {
    return (
        <CurrentUserProvider>
            <Layout content={<AppSidebar />}>
                {children}
            </Layout>
        </CurrentUserProvider>
    )
}