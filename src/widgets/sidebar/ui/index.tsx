"use client";

import { Vote, Users, LinkIcon, Calendar, Merge } from "lucide-react"
import Link from "next/link";

import {
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@/shared/ui/sidebar"
import {cn} from "@/shared/lib";
import {usePathname} from "next/navigation";
import {useIsSuperAdmin} from "@/entity/users/hooks/use-is-super-admin";
import {useMemo} from "react";

// Menu items.
const items = [
    {
        title: "Ovozlar",
        url: "/dashboard",
        icon: <Vote />,
    },
    {
        title: "Xo'jaliklar",
        url: "/dashboard/village-people",
        icon: <Users />,
    },
    {
        title: "Ovozlarni biriktirish",
        url: "/dashboard/connect-votes",
        icon: <LinkIcon />,
    },
    {
        title: "Kunlar bo'yicha ovozlar",
        url: "/dashboard/daily-votes",
        icon: <Calendar />,
    },
    {
        title: "Biriktirish",
        url: "/dashboard/biriktirish",
        icon: <Merge />,
        hide: true,
    },
]

export function AppSidebar() {
    const pathname = usePathname()
    const isSuperAdmin = useIsSuperAdmin()

    const itemsToShow = useMemo(() => {
        return items.filter(item => !item.hide || isSuperAdmin)
    }, [isSuperAdmin])
    return (
        <SidebarGroup>
            <SidebarGroupLabel>{"Ko'ktol ovozlar boshqaruv paneli"}</SidebarGroupLabel>
            <SidebarGroupContent>
                <SidebarMenu>
                    <>
                        {itemsToShow.map((item) => (
                            <SidebarMenuItem
                              key={item.title}
                              className={cn({
                                  'bg-sidebar-accent': item.url === pathname,
                              })}
                            >
                                <SidebarMenuButton asChild>
                                    <Link href={item.url}>
                                        {item.icon}
                                        <span>{item.title}</span>
                                    </Link>
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                        ))}
                    </>
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    )
}